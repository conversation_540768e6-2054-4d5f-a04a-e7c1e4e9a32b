{"ProjectDetails": {"Name": "", "Cost": "", "MainContractor": "", "Subcontractors": [], "KeyPersonel": {"Architect": "", "Engineer": ""}, "ContractType": "", "ProjectType": ["Residential", "Commercial", "Industrial", "MixedUse"]}, "SiteSpecification": {"SiteLocation": {"TotalArea": "", "LocationMap": ""}, "BuildingStats": {"Floors": "", "Basement": "", "Parking": ""}, "BuildingFootprint": {"BuildingArea": "", "BuiltArea": ""}, "UtilitiesServices": {"Water": "", "Electricity": "", "Sewer": "", "Internet": ""}, "AccessRoads": {"MainAccessRoads": [], "SecondaryAccessRoads": []}}, "RegulatoryCompliance": {"BuildingPermit": {"PermitNumber": "", "PermitSpecification": "", "PermitType": ""}, "Classification": {"BuildingClass": "", "ConstructionType": ""}, "FireSafetyRating": "", "ComplianceStandard": {"Accesibility": "", "Environmental": ""}, "OccupancyType": ""}, "SiteCommittee": {"CommitteeMembers": []}, "EmergencyContacts": {"Police": [], "FireDepartment": [], "MedicalServices": [], "EmergencyManagement": []}, "ProjectTimeline": {"StartDate": "", "EndDate": "", "Milestones": []}}