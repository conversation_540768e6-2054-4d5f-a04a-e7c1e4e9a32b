using HotChocolate.Types;
using Shared.GraphQL.Models;
using Shared.DTOs;

namespace Shared.GraphQL.Types
{
    public class SiteType : ObjectType<Site>
    {
        protected override void Configure(IObjectTypeDescriptor<Site> descriptor)
        {
            descriptor.Field(s => s.Id).Type<NonNullType<IdType>>();
            descriptor.Field(s => s.Name).Type<NonNullType<StringType>>();
            descriptor.Field(s => s.TenantId).Type<IdType>();
            descriptor.Field(s => s.SchemaVersion).Type<StringType>();
            
            // Cached/computed fields for quick queries
            descriptor.Field(s => s.Location).Type<StringType>();
            descriptor.Field(s => s.ProjectManager).Type<StringType>();
            descriptor.Field(s => s.Status).Type<StringType>();
            descriptor.Field(s => s.HealthStatus).Type<StringType>();
            descriptor.Field(s => s.ProjectType).Type<StringType>();
            descriptor.Field(s => s.StartDate).Type<DateTimeType>();
            descriptor.Field(s => s.EndDate).Type<DateTimeType>();
            descriptor.Field(s => s.ProgressPercentage).Type<DecimalType>();

            // Raw JSON data field
            descriptor.Field(s => s.SiteDataJson).Type<StringType>().Name("siteDataJson");
            
            // Computed field for structured site data
            descriptor.Field("siteData")
                .Type<SiteDataDtoType>()
                .Resolve(context =>
                {
                    var site = context.Parent<Site>();
                    return site.GetSiteData<SiteDataDto>();
                });

            // Audit fields
            descriptor.Field(s => s.CreatedAt).Type<NonNullType<DateTimeType>>();
            descriptor.Field(s => s.CreatedBy).Type<NonNullType<StringType>>();
            descriptor.Field(s => s.UpdatedAt).Type<DateTimeType>();
            descriptor.Field(s => s.UpdatedBy).Type<StringType>();
        }
    }

    // GraphQL type for SiteDataDto
    public class SiteDataDtoType : ObjectType<SiteDataDto>
    {
        protected override void Configure(IObjectTypeDescriptor<SiteDataDto> descriptor)
        {
            descriptor.Field(s => s.ProjectDetails).Type<ProjectDetailsDtoType>();
            descriptor.Field(s => s.SiteSpecification).Type<SiteSpecificationDtoType>();
            descriptor.Field(s => s.RegulatoryCompliance).Type<RegulatoryComplianceDtoType>();
            descriptor.Field(s => s.SiteCommittee).Type<SiteCommitteeDtoType>();
            descriptor.Field(s => s.EmergencyContacts).Type<EmergencyContactsDtoType>();
            descriptor.Field(s => s.ProjectTimeline).Type<ProjectTimelineDtoType>();
        }
    }

    public class ProjectDetailsDtoType : ObjectType<ProjectDetailsDto>
    {
        protected override void Configure(IObjectTypeDescriptor<ProjectDetailsDto> descriptor)
        {
            descriptor.Field(p => p.Name).Type<StringType>();
            descriptor.Field(p => p.Cost).Type<StringType>();
            descriptor.Field(p => p.MainContractor).Type<StringType>();
            descriptor.Field(p => p.Subcontractors).Type<ListType<StringType>>();
            descriptor.Field(p => p.KeyPersonel).Type<KeyPersonelDtoType>();
            descriptor.Field(p => p.ContractType).Type<StringType>();
            descriptor.Field(p => p.ProjectType).Type<ListType<StringType>>();
        }
    }

    public class KeyPersonelDtoType : ObjectType<KeyPersonelDto>
    {
        protected override void Configure(IObjectTypeDescriptor<KeyPersonelDto> descriptor)
        {
            descriptor.Field(k => k.Architect).Type<StringType>();
            descriptor.Field(k => k.Engineer).Type<StringType>();
        }
    }

    public class SiteSpecificationDtoType : ObjectType<SiteSpecificationDto>
    {
        protected override void Configure(IObjectTypeDescriptor<SiteSpecificationDto> descriptor)
        {
            descriptor.Field(s => s.SiteLocation).Type<SiteLocationDtoType>();
            descriptor.Field(s => s.BuildingStats).Type<BuildingStatsDtoType>();
            descriptor.Field(s => s.BuildingFootprint).Type<BuildingFootprintDtoType>();
            descriptor.Field(s => s.UtilitiesServices).Type<UtilitiesServicesDtoType>();
            descriptor.Field(s => s.AccessRoads).Type<AccessRoadsDtoType>();
        }
    }

    public class SiteLocationDtoType : ObjectType<SiteLocationDto>
    {
        protected override void Configure(IObjectTypeDescriptor<SiteLocationDto> descriptor)
        {
            descriptor.Field(s => s.TotalArea).Type<StringType>();
            descriptor.Field(s => s.LocationMap).Type<StringType>();
        }
    }

    public class BuildingStatsDtoType : ObjectType<BuildingStatsDto>
    {
        protected override void Configure(IObjectTypeDescriptor<BuildingStatsDto> descriptor)
        {
            descriptor.Field(b => b.Floors).Type<StringType>();
            descriptor.Field(b => b.Basement).Type<StringType>();
            descriptor.Field(b => b.Parking).Type<StringType>();
        }
    }

    public class BuildingFootprintDtoType : ObjectType<BuildingFootprintDto>
    {
        protected override void Configure(IObjectTypeDescriptor<BuildingFootprintDto> descriptor)
        {
            descriptor.Field(b => b.BuildingArea).Type<StringType>();
            descriptor.Field(b => b.BuiltArea).Type<StringType>();
        }
    }

    public class UtilitiesServicesDtoType : ObjectType<UtilitiesServicesDto>
    {
        protected override void Configure(IObjectTypeDescriptor<UtilitiesServicesDto> descriptor)
        {
            descriptor.Field(u => u.Water).Type<StringType>();
            descriptor.Field(u => u.Electricity).Type<StringType>();
            descriptor.Field(u => u.Sewer).Type<StringType>();
            descriptor.Field(u => u.Internet).Type<StringType>();
        }
    }

    public class AccessRoadsDtoType : ObjectType<AccessRoadsDto>
    {
        protected override void Configure(IObjectTypeDescriptor<AccessRoadsDto> descriptor)
        {
            descriptor.Field(a => a.MainAccessRoads).Type<ListType<StringType>>();
            descriptor.Field(a => a.SecondaryAccessRoads).Type<ListType<StringType>>();
        }
    }

    public class RegulatoryComplianceDtoType : ObjectType<RegulatoryComplianceDto>
    {
        protected override void Configure(IObjectTypeDescriptor<RegulatoryComplianceDto> descriptor)
        {
            descriptor.Field(r => r.BuildingPermit).Type<BuildingPermitDtoType>();
            descriptor.Field(r => r.Classification).Type<ClassificationDtoType>();
            descriptor.Field(r => r.FireSafetyRating).Type<StringType>();
            descriptor.Field(r => r.ComplianceStandard).Type<ComplianceStandardDtoType>();
            descriptor.Field(r => r.OccupancyType).Type<StringType>();
        }
    }

    public class BuildingPermitDtoType : ObjectType<BuildingPermitDto>
    {
        protected override void Configure(IObjectTypeDescriptor<BuildingPermitDto> descriptor)
        {
            descriptor.Field(b => b.PermitNumber).Type<StringType>();
            descriptor.Field(b => b.PermitSpecification).Type<StringType>();
            descriptor.Field(b => b.PermitType).Type<StringType>();
        }
    }

    public class ClassificationDtoType : ObjectType<ClassificationDto>
    {
        protected override void Configure(IObjectTypeDescriptor<ClassificationDto> descriptor)
        {
            descriptor.Field(c => c.BuildingClass).Type<StringType>();
            descriptor.Field(c => c.ConstructionType).Type<StringType>();
        }
    }

    public class ComplianceStandardDtoType : ObjectType<ComplianceStandardDto>
    {
        protected override void Configure(IObjectTypeDescriptor<ComplianceStandardDto> descriptor)
        {
            descriptor.Field(c => c.Accessibility).Type<StringType>();
            descriptor.Field(c => c.Environmental).Type<StringType>();
        }
    }

    public class SiteCommitteeDtoType : ObjectType<SiteCommitteeDto>
    {
        protected override void Configure(IObjectTypeDescriptor<SiteCommitteeDto> descriptor)
        {
            descriptor.Field(s => s.CommitteeMembers).Type<ListType<StringType>>();
        }
    }

    public class EmergencyContactsDtoType : ObjectType<EmergencyContactsDto>
    {
        protected override void Configure(IObjectTypeDescriptor<EmergencyContactsDto> descriptor)
        {
            descriptor.Field(e => e.Police).Type<ListType<StringType>>();
            descriptor.Field(e => e.FireDepartment).Type<ListType<StringType>>();
            descriptor.Field(e => e.MedicalServices).Type<ListType<StringType>>();
            descriptor.Field(e => e.EmergencyManagement).Type<ListType<StringType>>();
        }
    }

    public class ProjectTimelineDtoType : ObjectType<ProjectTimelineDto>
    {
        protected override void Configure(IObjectTypeDescriptor<ProjectTimelineDto> descriptor)
        {
            descriptor.Field(p => p.StartDate).Type<StringType>();
            descriptor.Field(p => p.EndDate).Type<StringType>();
            descriptor.Field(p => p.Milestones).Type<ListType<StringType>>();
        }
    }
}
