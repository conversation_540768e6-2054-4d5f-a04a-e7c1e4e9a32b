using Shared.GraphQL.Models;
using Shared.DTOs;

namespace GraphQLApi.Services
{
    public interface ISiteService
    {
        // Basic CRUD operations
        Task<IEnumerable<Site>> GetAllSitesAsync(Guid? tenantId = null);
        Task<Site?> GetSiteByIdAsync(Guid id, Guid? tenantId = null);
        Task<Site> CreateSiteAsync(string name, SiteDataDto? siteData = null, Guid? tenantId = null);
        Task<Site?> UpdateSiteAsync(Guid id, string? name = null, SiteDataDto? siteData = null, Guid? tenantId = null);
        Task<bool> DeleteSiteAsync(Guid id, Guid? tenantId = null);
        
        // Site data specific operations
        Task<SiteDataDto?> GetSiteDataAsync(Guid id, Guid? tenantId = null);
        Task<Site?> UpdateSiteDataAsync(Guid id, SiteDataDto siteData, Guid? tenantId = null);
        Task<Site?> PatchSiteDataAsync(Guid id, string jsonPatch, Guid? tenantId = null);
        
        // Query operations
        Task<IEnumerable<Site>> GetSitesByStatusAsync(string status, Guid? tenantId = null);
        Task<IEnumerable<Site>> GetSitesByProjectManagerAsync(string projectManager, Guid? tenantId = null);
        Task<IEnumerable<Site>> GetSitesByProjectTypeAsync(string projectType, Guid? tenantId = null);
        Task<IEnumerable<Site>> SearchSitesAsync(string searchTerm, Guid? tenantId = null);
        
        // Utility operations
        Task<bool> SiteExistsAsync(Guid id, Guid? tenantId = null);
        Task<bool> ValidateSiteDataAsync(SiteDataDto siteData);
        Task<Site?> CloneSiteAsync(Guid sourceId, string newName, Guid? tenantId = null);
    }
}
