using Microsoft.EntityFrameworkCore;
using GraphQLApi.Data;
using GraphQLApi.Services;
using GraphQLApi.GraphQL.Queries;
using GraphQLApi.GraphQL.Mutations;
using Shared.GraphQL.Models;
using Shared.GraphQL.Types;
using HotChocolate.Data;
using Microsoft.Extensions.Options;
using Minio;
using Shared.Configuration;

namespace GraphQLApi.Extensions
{
    public static class ServiceCollectionExtensions
    {

        public static IServiceCollection AddApplicationServices(this IServiceCollection services)
        {
            services.AddScoped<IWorkerService, WorkerService>();
            services.AddScoped<ITrainingService, TrainingService>();
            services.AddScoped<ITradeService, TradeService>();
            services.AddScoped<ISkillService, SkillService>();
            services.AddScoped<IPhotoService, PhotoService>();
            services.AddScoped<IWorkerAttendanceService, WorkerAttendanceService>();
            services.AddScoped<ITrainingStatusService, TrainingStatusService>();
            services.AddScoped<ITaskService, TaskService>();
            services.AddScoped<IEquipmentService, EquipmentService>();
            services.AddScoped<IRelationshipService, RelationshipService>();
            services.AddHostedService<TrainingStatusBackgroundService>();

            return services;
        }

        public static IServiceCollection AddHikvisionServices(this IServiceCollection services, IConfiguration configuration)
        {
            var baseUrl = configuration["HikvisionApi:BaseUrl"]
                ?? throw new ArgumentNullException("HikvisionApi:BaseUrl is missing in configuration");

            services.AddHttpClient<IHikvisionService, HikvisionService>(client =>
            {
                client.BaseAddress = new Uri(baseUrl);
            });
            services.AddScoped<IHikvisionService, HikvisionService>();

            return services;
        }

        public static IServiceCollection AddMinIOServices(this IServiceCollection services, IConfiguration configuration)
        {
            // Configure MinIO settings
            services.Configure<MinIOConfiguration>(configuration.GetSection("MinIO"));

            // Register MinIO client
            services.AddSingleton<IMinioClient>(serviceProvider =>
            {
                var config = serviceProvider.GetRequiredService<IOptions<MinIOConfiguration>>().Value;

                var minioClient = new MinioClient()
                    .WithEndpoint(config.Endpoint)
                    .WithCredentials(config.AccessKey, config.SecretKey);

                if (config.UseSSL)
                {
                    minioClient = minioClient.WithSSL();
                }

                if (!string.IsNullOrEmpty(config.Region))
                {
                    minioClient = minioClient.WithRegion(config.Region);
                }

                return minioClient.Build();
            });

            // Register MinIO services
            services.AddScoped<IMinioService, MinioService>();
            services.AddScoped<IBucketInitializationService, BucketInitializationService>();
            services.AddHostedService<BucketInitializationHostedService>();

            return services;
        }

        public static IServiceCollection AddGraphQLServices(this IServiceCollection services)
        {
            services
                .AddGraphQLServer()
                .ModifyRequestOptions(opt => opt.IncludeExceptionDetails = true)
                .AddQueryType<Query>()
                .AddMutationType<Mutation>()
                .AddProjections()
                .AddFiltering()
                // .AddMutationConventions()
                .AddType<UploadType>()
                .AddType<WorkerType>()
                .AddType<TrainingType>()
                .AddType<TradeType>()
                .AddType<SkillType>()
                .AddType<TaskType>()
                .AddType<EquipmentType>()
                .AddType<WorkerAttendanceType>()
                .AddType<ToolboxSessionType>()
                .AddType<WorkerTrainingHistoryType>()
                .AddType<IncidentType>()
                .AddType<EnumType<Shared.Enums.TrainingStatus>>()
                .AddType<EnumType<Shared.Enums.TaskStatus>>()
                .AddType<EnumType<Shared.Enums.TaskPriority>>()
                .AddType<EnumType<Shared.Enums.InspectionStatus>>()
                .AddType<EnumType<Shared.Enums.IncidentStatus>>()
                .AddType<Shared.GraphQL.Types.FileMetadataType>()
                .AddType<Shared.GraphQL.Types.DocumentFileType>()
                .AddType<EnumType<Shared.Enums.AllowedFileType>>()
                .AddType<EnumType<Shared.Enums.BucketName>>();

            return services;
        }
    }
}