using GraphQLApi.Data;
using GraphQLApi.Services;
using Microsoft.EntityFrameworkCore;
using Shared.GraphQL.Models;
using HotChocolate;
using Shared.Utils;
using Shared.Enums;
using GraphQLApi.GraphQL.Types;
using Task = Shared.GraphQL.Models.Task;

namespace GraphQLApi.GraphQL.Mutations
{
    public class Mutation
    {
        private readonly IWorkerService _workerService;
        private readonly ITrainingService _trainingService;
        private readonly ITradeService _tradeService;
        private readonly ISkillService _skillService;
        private readonly ITrainingStatusService _trainingStatusService;
        private readonly ITaskService _taskService;
        private readonly IEquipmentService _equipmentService;
        private readonly ISiteService _siteService;
        private readonly IDbContextFactory<AppDbContext> _contextFactory;
        private readonly IRelationshipService _relationshipService;
        private readonly IMinioService _minioService;
        private readonly ILogger<Mutation> _logger;

        public Mutation(
            IWorkerService workerService,
            ITrainingService trainingService,
            ITradeService tradeService,
            ISkillService skillService,
            ITrainingStatusService trainingStatusService,
            ITaskService taskService,
            IEquipmentService equipmentService,
            ISiteService siteService,
            IDbContextFactory<AppDbContext> contextFactory,
            IRelationshipService relationshipService,
            IMinioService minioService,
            ILogger<Mutation> logger)
        {
            _workerService = workerService;
            _trainingService = trainingService;
            _tradeService = tradeService;
            _skillService = skillService;
            _trainingStatusService = trainingStatusService;
            _taskService = taskService;
            _equipmentService = equipmentService;
            _siteService = siteService;
            _contextFactory = contextFactory;
            _relationshipService = relationshipService;
            _minioService = minioService;
            _logger = logger;
        }

        // Worker Mutations
        public async Task<Worker> CreateWorker(
            CreateWorkerInput input
           )
        {
            string name = input.Name;
            string company = input.Company;
            string nationalId = input.NationalId;
            string gender = input.Gender;
            string phoneNumber = input.PhoneNumber;
            DateOnly? dateOfBirth = input.DateOfBirth;
            List<int>? trainingIds = input.TrainingIds;
            List<int>? tradeIds = input.TradeIds;
            List<int>? skillIds = input.SkillIds;
            string? mpesaNumber = input.MpesaNumber;
            string? email = input.Email;
            DateTime? inductionDate = input.InductionDate;
            DateTime? medicalCheckDate = input.MedicalCheckDate;
            IFile? profilePicture = input.ProfilePicture;
            IFile? signature = input.Signature;
            List<DocumentFileInput>? documents = input.Documents;

            trainingIds ??= new List<int>();
            tradeIds ??= new List<int>();
            skillIds ??= new List<int>();

            try
            {
                var worker = new Worker
                {
                    Name = name,
                    Company = company,
                    NationalId = nationalId,
                    Gender = gender,
                    DateOfBirth = dateOfBirth,
                    ManHours = 0,
                    Rating = 0,
                    PhoneNumber = phoneNumber,
                    MpesaNumber = mpesaNumber,
                    Email = email,
                    InductionDate = inductionDate,
                    MedicalCheckDate = medicalCheckDate
                };

                // Create worker first
                var createdWorker = await _workerService.CreateWorkerAsync(worker);

                // Add relationships if specified using the relationship service
                if (trainingIds.Any())
                {
                    await _relationshipService.AssignTrainingsToWorkerAsync(createdWorker.Id, trainingIds);
                }
                if (tradeIds.Any())
                {
                    await _relationshipService.AssignTradesToWorkerAsync(createdWorker.Id, tradeIds);
                }
                if (skillIds.Any())
                {
                    await _relationshipService.AssignSkillsToWorkerAsync(createdWorker.Id, skillIds);
                }

                // Handle file uploads
                using var context = await _contextFactory.CreateDbContextAsync();
                var workerToUpdate = await context.Workers.FindAsync(createdWorker.Id);
                if (workerToUpdate != null)
                {
                    // Upload profile picture
                    if (profilePicture != null)
                    {
                        using var profileStream = profilePicture.OpenReadStream();
                        var profileMetadata = await _minioService.UploadFileAsync(
                            profileStream,
                            profilePicture.Name,
                            "profile-picture",
                            profilePicture.ContentType ?? "image/jpeg",
                            "Worker profile picture",
                            $"worker-{createdWorker.Id}",
                            false,
                            null);

                        if (profileMetadata != null)
                        {
                            workerToUpdate.ProfilePictureFileId = profileMetadata.Id;
                        }
                    }

                    // Upload signature
                    if (signature != null)
                    {
                        using var signatureStream = signature.OpenReadStream();
                        var signatureMetadata = await _minioService.UploadFileAsync(
                            signatureStream,
                            signature.Name,
                            "signatures",
                            signature.ContentType ?? "image/png",
                            "Worker signature",
                            $"worker-{createdWorker.Id}",
                            false,
                            null);

                        if (signatureMetadata != null)
                        {
                            workerToUpdate.SignatureFileId = signatureMetadata.Id;
                        }
                    }

                    // Upload documents
                    if (documents != null && documents.Any())
                    {
                        foreach (var doc in documents)
                        {
                            using var docStream = doc.File.OpenReadStream();
                            var docMetadata = await _minioService.UploadFileAsync(
                                docStream,
                                doc.File.Name,
                                "docs",
                                doc.File.ContentType ?? "application/pdf",
                                doc.Description,
                                doc.FolderPath ?? $"worker-{createdWorker.Id}",
                                doc.IsPublic,
                                doc.ExpiresAt);

                            if (docMetadata != null)
                            {
                                var documentFile = new DocumentFile
                                {
                                    Name = doc.Name,
                                    FileMetadataId = docMetadata.Id,
                                    CreatedAt = DateTime.UtcNow,
                                    CreatedBy = "System" // TODO: Get from current user context
                                };

                                context.DocumentFiles.Add(documentFile);

                                // Set discriminator fields for polymorphic relationship
                                context.Entry(documentFile).Property("EntityType").CurrentValue = "Worker";
                                context.Entry(documentFile).Property("WorkerId").CurrentValue = createdWorker.Id;
                            }
                        }
                    }

                    await context.SaveChangesAsync();
                }

                return await _workerService.GetWorkerByIdAsync(createdWorker.Id) ?? createdWorker;
            }
            catch (GraphQLException)
            {
                throw;
            }
            catch (Exception ex)
            {
                throw new GraphQLException(new Error("Internal Server Error", ex.Message));
            }
        }

        public async Task<Worker?> UpdateWorker(
            UpdateWorkerInput input
           )
        {
            int id = input.Id;
            string? name = input.Name;
            string? company = input.Company;
            DateOnly? dateOfBirth = input.DateOfBirth;
            List<int>? trainingIds = input.TrainingIds;
            List<int>? tradeIds = input.TradeIds;
            List<int>? skillIds = input.SkillIds;
            int? manHours = input.ManHours;
            double? rating = input.Rating;
            string? gender = input.Gender;
            string? phoneNumber = input.PhoneNumber;
            string? mpesaNumber = input.MpesaNumber;
            string? email = input.Email;
            DateTime? inductionDate = input.InductionDate;
            DateTime? medicalCheckDate = input.MedicalCheckDate;
            IFile? profilePicture = input.ProfilePicture;
            IFile? signature = input.Signature;
            List<DocumentFileInput>? documents = input.Documents;

            var existingWorker = await _workerService.GetWorkerByIdAsync(id);
            if (existingWorker == null)
                return null;

            var updatedWorker = new Worker
            {
                Id = existingWorker.Id,
                Name = name ?? existingWorker.Name,
                Company = company ?? existingWorker.Company,
                DateOfBirth = dateOfBirth ?? existingWorker.DateOfBirth,
                ManHours = manHours ?? existingWorker.ManHours,
                Rating = rating ?? existingWorker.Rating,
                Gender = gender ?? existingWorker.Gender,
                NationalId = existingWorker.NationalId,
                PhoneNumber = phoneNumber ?? existingWorker.PhoneNumber,
                MpesaNumber = mpesaNumber ?? existingWorker.MpesaNumber,
                Email = email ?? existingWorker.Email,
                InductionDate = inductionDate ?? existingWorker.InductionDate,
                MedicalCheckDate = medicalCheckDate ?? existingWorker.MedicalCheckDate
            };

            var result = await _workerService.UpdateWorkerAsync(id, updatedWorker);

            // Update relationships if specified using the relationship service
            if (trainingIds != null || tradeIds != null || skillIds != null)
            {
                await _relationshipService.UpdateWorkerRelationshipsAsync(id, trainingIds, tradeIds, skillIds);
            }

            // Handle file uploads
            using var context = await _contextFactory.CreateDbContextAsync();
            var workerToUpdate = await context.Workers.FindAsync(id);
            if (workerToUpdate != null)
            {
                // Upload profile picture
                if (profilePicture != null)
                {
                    using var profileStream = profilePicture.OpenReadStream();
                    var profileMetadata = await _minioService.UploadFileAsync(
                        profileStream,
                        profilePicture.Name,
                        "profile-picture",
                        profilePicture.ContentType ?? "image/jpeg",
                        "Worker profile picture",
                        $"worker-{id}",
                        false,
                        null);

                    if (profileMetadata != null)
                    {
                        workerToUpdate.ProfilePictureFileId = profileMetadata.Id;
                    }
                }

                // Upload signature
                if (signature != null)
                {
                    using var signatureStream = signature.OpenReadStream();
                    var signatureMetadata = await _minioService.UploadFileAsync(
                        signatureStream,
                        signature.Name,
                        "signatures",
                        signature.ContentType ?? "image/png",
                        "Worker signature",
                        $"worker-{id}",
                        false,
                        null);

                    if (signatureMetadata != null)
                    {
                        workerToUpdate.SignatureFileId = signatureMetadata.Id;
                    }
                }

                // Upload documents
                if (documents != null && documents.Any())
                {
                    foreach (var doc in documents)
                    {
                        using var docStream = doc.File.OpenReadStream();
                        var docMetadata = await _minioService.UploadFileAsync(
                            docStream,
                            doc.File.Name,
                            "docs",
                            doc.File.ContentType ?? "application/pdf",
                            doc.Description,
                            doc.FolderPath ?? $"worker-{id}",
                            doc.IsPublic,
                            doc.ExpiresAt);

                        if (docMetadata != null)
                        {
                            var documentFile = new DocumentFile
                            {
                                Name = doc.Name,
                                FileMetadataId = docMetadata.Id,
                                CreatedAt = DateTime.UtcNow,
                                CreatedBy = "System" // TODO: Get from current user context
                            };

                            context.DocumentFiles.Add(documentFile);

                            // Set discriminator fields for polymorphic relationship
                            context.Entry(documentFile).Property("EntityType").CurrentValue = "Worker";
                            context.Entry(documentFile).Property("WorkerId").CurrentValue = id;
                        }
                    }
                }

                await context.SaveChangesAsync();
            }

            var finalWorker = await _workerService.GetWorkerByIdAsync(id);
            if (finalWorker == null)
            {
                throw new InvalidOperationException($"Worker with id {id} was not found after update. It may have been deleted during the update process.");
            }
            return finalWorker;
        }

        public async Task<bool> DeleteWorker(int id)
        {
            return await _workerService.DeleteWorkerAsync(id);
        }

        // Training Mutations
        public async Task<Training> CreateTraining(
            string name,
            string? description = null,
            DateTime? startDate = null,
            DateTime? endDate = null,
            string? duration = null,
            int? validityPeriodMonths = null,
            string? trainingType = null,
            string? trainer = null,
            string? frequency = null,
            TrainingStatus status = TrainingStatus.Scheduled,
            List<int>? workerIds = null,
            List<DocumentFileInput>? documents = null)
        {
            workerIds ??= new List<int>();

            try
            {
                // Validate duration format if provided
                if (!string.IsNullOrEmpty(duration))
                {
                    try
                    {
                        TrainingDuration.Parse(duration);
                    }
                    catch (FormatException ex)
                    {
                        throw new GraphQLException(new Error(
                            "Validation",
                            ex.Message)
                        );
                    }
                }

                var training = new Training
                {
                    Name = name,
                    Description = description,
                    StartDate = startDate,
                    EndDate = endDate,
                    Duration = duration,
                    ValidityPeriodMonths = validityPeriodMonths,
                    TrainingType = trainingType,
                    Trainer = trainer,
                    Frequency = frequency,
                    Status = status
                };

                var createdTraining = await _trainingService.CreateTrainingAsync(training);

                // Add workers if specified using the relationship service
                if (workerIds.Any())
                {
                    await _relationshipService.AssignWorkersToTrainingAsync(createdTraining.Id, workerIds);
                }

                // Handle document uploads
                if (documents != null && documents.Any())
                {
                    using var context = await _contextFactory.CreateDbContextAsync();

                    foreach (var doc in documents)
                    {
                        using var docStream = doc.File.OpenReadStream();
                        var docMetadata = await _minioService.UploadFileAsync(
                            docStream,
                            doc.File.Name,
                            "docs",
                            doc.File.ContentType ?? "application/pdf",
                            doc.Description,
                            doc.FolderPath ?? $"training-{createdTraining.Id}",
                            doc.IsPublic,
                            doc.ExpiresAt);

                        if (docMetadata != null)
                        {
                            var documentFile = new DocumentFile
                            {
                                Name = doc.Name,
                                FileMetadataId = docMetadata.Id,
                                CreatedAt = DateTime.UtcNow,
                                CreatedBy = "System" // TODO: Get from current user context
                            };

                            context.DocumentFiles.Add(documentFile);

                            // Set discriminator fields for polymorphic relationship
                            context.Entry(documentFile).Property("EntityType").CurrentValue = "Training";
                            context.Entry(documentFile).Property("TrainingId").CurrentValue = createdTraining.Id;
                        }
                    }

                    await context.SaveChangesAsync();
                }

                return await _trainingService.GetTrainingByIdAsync(createdTraining.Id) ?? createdTraining;
            }
            catch (GraphQLException)
            {
                throw;
            }
            catch (Exception ex)
            {
                throw new GraphQLException(new Error("Internal Server Error", ex.Message));
            }
        }

        public async Task<Training?> UpdateTraining(
            int id,
            string? name = null,
            string? description = null,
            DateTime? startDate = null,
            DateTime? endDate = null,
            string? duration = null,
            int? validityPeriodMonths = null,
            string? trainingType = null,
            string? trainer = null,
            string? frequency = null,
            TrainingStatus? status = null,
            List<int>? workerIds = null,
            List<DocumentFileInput>? documents = null)
        {
            var existingTraining = await _trainingService.GetTrainingByIdAsync(id);
            if (existingTraining == null)
            {
                throw new GraphQLException(new Error(
                    "NotFound",
                    $"Training with ID {id} not found.")
                );
            }

            // Validate duration format if provided
            if (duration != null)
            {
                if (string.IsNullOrEmpty(duration))
                {
                    duration = null;
                }
                else
                {
                    try
                    {
                        TrainingDuration.Parse(duration);
                    }
                    catch (FormatException ex)
                    {
                        throw new GraphQLException(new Error(
                            "Validation",
                            ex.Message)
                        );
                    }
                }
            }

            var updatedTraining = new Training
            {
                Id = existingTraining.Id,
                Name = name ?? existingTraining.Name,
                Description = description ?? existingTraining.Description,
                StartDate = startDate ?? existingTraining.StartDate,
                EndDate = endDate ?? existingTraining.EndDate,
                Duration = duration ?? existingTraining.Duration,
                ValidityPeriodMonths = validityPeriodMonths ?? existingTraining.ValidityPeriodMonths,
                TrainingType = trainingType ?? existingTraining.TrainingType,
                Trainer = trainer ?? existingTraining.Trainer,
                Frequency = frequency ?? existingTraining.Frequency,
                Status = status ?? existingTraining.Status
            };

            var result = await _trainingService.UpdateTrainingAsync(id, updatedTraining);

            // Update workers if specified using the relationship service
            if (workerIds != null)
            {
                await _relationshipService.UpdateTrainingRelationshipsAsync(id, workerIds);
            }

            // Handle document uploads
            if (documents != null && documents.Any())
            {
                using var context = await _contextFactory.CreateDbContextAsync();

                foreach (var doc in documents)
                {
                    using var docStream = doc.File.OpenReadStream();
                    var docMetadata = await _minioService.UploadFileAsync(
                        docStream,
                        doc.File.Name,
                        "docs",
                        doc.File.ContentType ?? "application/pdf",
                        doc.Description,
                        doc.FolderPath ?? $"training-{id}",
                        doc.IsPublic,
                        doc.ExpiresAt);

                    if (docMetadata != null)
                    {
                        var documentFile = new DocumentFile
                        {
                            Name = doc.Name,
                            FileMetadataId = docMetadata.Id,
                            CreatedAt = DateTime.UtcNow,
                            CreatedBy = "System" // TODO: Get from current user context
                        };

                        context.DocumentFiles.Add(documentFile);

                        // Set discriminator fields for polymorphic relationship
                        context.Entry(documentFile).Property("EntityType").CurrentValue = "Training";
                        context.Entry(documentFile).Property("TrainingId").CurrentValue = id;
                    }
                }

                await context.SaveChangesAsync();
            }

            return await _trainingService.GetTrainingByIdAsync(id);
        }

        public async Task<bool> DeleteTraining(int id)
        {
            return await _trainingService.DeleteTrainingAsync(id);
        }

        public async Task<int> AssignTrainingToWorkersByTrade(
            int trainingId,
            List<int> tradeIds)
        {
            try
            {
                await using var context = await _contextFactory.CreateDbContextAsync();

                // Get the training
                var training = await context.Trainings
                    .Include(t => t.Workers)
                    .FirstOrDefaultAsync(t => t.Id == trainingId);

                if (training == null)
                {
                    throw new GraphQLException(new Error(
                        "NotFound",
                        $"Training with ID {trainingId} not found.")
                    );
                }

                // Get all workers with the specified trades
                var workersToAssign = await context.Workers
                    .Include(w => w.Trades)
                    .Where(w => w.Trades.Any(t => tradeIds.Contains(t.Id)))
                    .ToListAsync();

                int assignedCount = 0;
                foreach (var worker in workersToAssign)
                {
                    // Only add if not already assigned
                    if (!training.Workers.Contains(worker))
                    {
                        training.Workers.Add(worker);
                        assignedCount++;
                    }
                }

                await context.SaveChangesAsync();
                return assignedCount;
            }
            catch (GraphQLException)
            {
                throw;
            }
            catch (Exception ex)
            {
                throw new GraphQLException(new Error("Internal Server Error", ex.Message));
            }
        }

        public async Task<WorkerTrainingHistory> CompleteTraining(
            int workerId,
            int trainingId,
            DateTime? completionDate = null,
            decimal? score = null,
            string? notes = null)
        {
            var actualCompletionDate = completionDate ?? DateTime.UtcNow;
            return await _trainingStatusService.CompleteTrainingAsync(workerId, trainingId, actualCompletionDate, score, notes);
        }

        public async Task<bool> UpdateTrainingStatuses()
        {
            try
            {
                await _trainingStatusService.UpdateTrainingStatusesAsync();
                await _trainingStatusService.UpdateTrainingHistoryStatusesAsync();
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "An error occurred while updating training statuses.");
                return false;
            }
        }

        // Trade Mutations
        public async Task<Trade> CreateTrade(
            string name,
            string? description = null,
            List<int>? workerIds = null)
        {
            workerIds ??= new List<int>();

            try
            {
                var trade = new Trade
                {
                    Name = name,
                    Description = description
                };

                var createdTrade = await _tradeService.CreateTradeAsync(trade);

                // Add workers if specified
                if (workerIds.Any())
                {
                    await using var context = await _contextFactory.CreateDbContextAsync();
                    var tradeEntity = await context.Trades
                        .Include(t => t.Workers)
                        .FirstOrDefaultAsync(t => t.Id == createdTrade.Id);

                    if (tradeEntity != null)
                    {
                        foreach (var workerId in workerIds)
                        {
                            var worker = await context.Workers.FindAsync(workerId);
                            if (worker != null)
                            {
                                tradeEntity.Workers.Add(worker);
                            }
                        }
                        await context.SaveChangesAsync();
                    }
                }

                return await _tradeService.GetTradeByIdAsync(createdTrade.Id) ?? createdTrade;
            }
            catch (GraphQLException)
            {
                throw;
            }
            catch (Exception ex)
            {
                throw new GraphQLException(new Error("Internal Server Error", ex.Message));
            }
        }

        public async Task<Trade?> UpdateTrade(
            int id,
            string? name = null,
            string? description = null,
            List<int>? workerIds = null)
        {
            var existingTrade = await _tradeService.GetTradeByIdAsync(id);
            if (existingTrade == null)
                return null;

            var updatedTrade = new Trade
            {
                Id = existingTrade.Id,
                Name = name ?? existingTrade.Name,
                Description = description ?? existingTrade.Description
            };

            var result = await _tradeService.UpdateTradeAsync(id, updatedTrade, "System");

            // Update workers if specified
            if (workerIds != null)
            {
                await using var context = await _contextFactory.CreateDbContextAsync();
                var tradeEntity = await context.Trades
                    .Include(t => t.Workers)
                    .FirstOrDefaultAsync(t => t.Id == id);

                if (tradeEntity != null)
                {
                    // Clear existing workers
                    tradeEntity.Workers.Clear();

                    // Add new workers
                    foreach (var workerId in workerIds)
                    {
                        var worker = await context.Workers.FindAsync(workerId);
                        if (worker != null)
                        {
                            tradeEntity.Workers.Add(worker);
                        }
                    }
                    await context.SaveChangesAsync();
                }
            }

            return await _tradeService.GetTradeByIdAsync(id);
        }

        public async Task<bool> DeleteTrade(int id)
        {
            return await _tradeService.DeleteTradeAsync(id);
        }

        // Skill Mutations
        public async Task<Skill> CreateSkill(
            string name,
            string? description = null,
            List<int>? workerIds = null)
        {
            workerIds ??= new List<int>();

            try
            {
                var skill = new Skill
                {
                    Name = name,
                    Description = description
                };

                var createdSkill = await _skillService.CreateSkillAsync(skill);

                // Add workers if specified
                if (workerIds.Any())
                {
                    await using var context = await _contextFactory.CreateDbContextAsync();
                    var skillEntity = await context.Skills
                        .Include(s => s.Workers)
                        .FirstOrDefaultAsync(s => s.Id == createdSkill.Id);

                    if (skillEntity != null)
                    {
                        foreach (var workerId in workerIds)
                        {
                            var worker = await context.Workers.FindAsync(workerId);
                            if (worker != null)
                            {
                                skillEntity.Workers.Add(worker);
                            }
                        }
                        await context.SaveChangesAsync();
                    }
                }

                return await _skillService.GetSkillByIdAsync(createdSkill.Id) ?? createdSkill;
            }
            catch (GraphQLException)
            {
                throw;
            }
            catch (Exception ex)
            {
                throw new GraphQLException(new Error("Internal Server Error", ex.Message));
            }
        }

        public async Task<Skill?> UpdateSkill(
            int id,
            string? name = null,
            string? description = null,
            List<int>? workerIds = null)
        {
            var existingSkill = await _skillService.GetSkillByIdAsync(id);
            if (existingSkill == null)
                return null;

            var updatedSkill = new Skill
            {
                Id = existingSkill.Id,
                Name = name ?? existingSkill.Name,
                Description = description ?? existingSkill.Description
            };

            var result = await _skillService.UpdateSkillAsync(id, updatedSkill, "System");

            // Update workers if specified
            if (workerIds != null)
            {
                await using var context = await _contextFactory.CreateDbContextAsync();
                var skillEntity = await context.Skills
                    .Include(s => s.Workers)
                    .FirstOrDefaultAsync(s => s.Id == id);

                if (skillEntity != null)
                {
                    // Clear existing workers
                    skillEntity.Workers.Clear();

                    // Add new workers
                    foreach (var workerId in workerIds)
                    {
                        var worker = await context.Workers.FindAsync(workerId);
                        if (worker != null)
                        {
                            skillEntity.Workers.Add(worker);
                        }
                    }
                    await context.SaveChangesAsync();
                }
            }

            return await _skillService.GetSkillByIdAsync(id);
        }

        public async Task<bool> DeleteSkill(int id)
        {
            return await _skillService.DeleteSkillAsync(id);
        }

        // Task Mutations
        public async Task<Task> CreateTask(
            string name,
            string type,
            string description,
            Shared.Enums.TaskStatus status = Shared.Enums.TaskStatus.TODO,
            Shared.Enums.TaskPriority priority = Shared.Enums.TaskPriority.MEDIUM,
            string? timeForCompletion = null,
            DateTime? dueDate = null,
            DateTime? startDate = null,
            string? category = null,
            Shared.Enums.InspectionStatus inspectionStatus = Shared.Enums.InspectionStatus.NOT_REQUIRED,
            string? associatedMethodStatement = null,
            int? chiefEngineerId = null,
            List<int>? workerIds = null,
            List<int>? equipmentIds = null)
        {
            // Validate required fields
            if (string.IsNullOrWhiteSpace(name))
            {
                throw new GraphQLException(new Error("Validation", "Task name is required."));
            }
            if (string.IsNullOrWhiteSpace(type))
            {
                throw new GraphQLException(new Error("Validation", "Task type is required."));
            }
            if (string.IsNullOrWhiteSpace(description))
            {
                throw new GraphQLException(new Error("Validation", "Task description is required."));
            }

            workerIds ??= new List<int>();
            equipmentIds ??= new List<int>();

            try
            {
                var task = new Task
                {
                    Name = name,
                    Type = type,
                    Description = description,
                    Status = status,
                    Priority = priority,
                    TimeForCompletion = timeForCompletion,
                    DueDate = dueDate,
                    StartDate = startDate,
                    Category = category,
                    InspectionStatus = inspectionStatus,
                    AssociatedMethodStatement = associatedMethodStatement,
                    ChiefEngineerId = chiefEngineerId
                };

                var createdTask = await _taskService.CreateTaskAsync(task);

                // Add relationships if specified using the relationship service
                if (workerIds.Any())
                {
                    await _relationshipService.AssignWorkersToTaskAsync(createdTask.Id, workerIds);
                }
                if (equipmentIds.Any())
                {
                    await _relationshipService.AssignEquipmentToTaskAsync(createdTask.Id, equipmentIds);
                }

                return await _taskService.GetTaskByIdAsync(createdTask.Id) ?? createdTask;
            }
            catch (GraphQLException)
            {
                throw;
            }
            catch (Exception ex)
            {
                throw new GraphQLException(new Error("Internal Server Error", ex.Message));
            }
        }

        public async Task<Task?> UpdateTask(
            int id,
            string? name = null,
            string? type = null,
            string? description = null,
            Shared.Enums.TaskStatus? status = null,
            Shared.Enums.TaskPriority? priority = null,
            string? timeForCompletion = null,
            DateTime? dueDate = null,
            DateTime? startDate = null,
            string? category = null,
            Shared.Enums.InspectionStatus? inspectionStatus = null,
            string? associatedMethodStatement = null,
            int? chiefEngineerId = null,
            List<int>? workerIds = null,
            List<int>? equipmentIds = null)
        {
            var existingTask = await _taskService.GetTaskByIdAsync(id);
            if (existingTask == null)
                return null;

            var updatedTask = new Task
            {
                Id = existingTask.Id,
                Name = name ?? existingTask.Name,
                Type = type ?? existingTask.Type,
                Description = description ?? existingTask.Description,
                Status = status ?? existingTask.Status,
                TaskNumber = existingTask.TaskNumber, // Keep existing task number
                Priority = priority ?? existingTask.Priority,
                TimeForCompletion = timeForCompletion ?? existingTask.TimeForCompletion,
                DueDate = dueDate ?? existingTask.DueDate,
                StartDate = startDate ?? existingTask.StartDate,
                Category = category ?? existingTask.Category,
                InspectionStatus = inspectionStatus ?? existingTask.InspectionStatus,
                AssociatedMethodStatement = associatedMethodStatement ?? existingTask.AssociatedMethodStatement,
                ChiefEngineerId = chiefEngineerId ?? existingTask.ChiefEngineerId
            };

            var result = await _taskService.UpdateTaskAsync(id, updatedTask);

            // Update relationships if specified using the relationship service
            if (workerIds != null || equipmentIds != null)
            {
                await _relationshipService.UpdateTaskRelationshipsAsync(id, workerIds, equipmentIds);
            }

            return await _taskService.GetTaskByIdAsync(id);
        }

        public async Task<bool> DeleteTask(int id)
        {
            return await _taskService.DeleteTaskAsync(id);
        }

        // Equipment Mutations
        public async Task<Equipment> CreateEquipment(
            string name,
            string? description = null,
            string? serialNumber = null,
            string? model = null,
            string? manufacturer = null,
            DateTime? purchaseDate = null,
            DateTime? lastMaintenanceDate = null,
            DateTime? nextMaintenanceDate = null,
            string? location = null,
            string? status = null,
            decimal? purchasePrice = null,
            string? category = null)
        {
            try
            {
                var equipment = new Equipment
                {
                    Name = name,
                    Description = description,
                    SerialNumber = serialNumber,
                    Model = model,
                    Manufacturer = manufacturer,
                    PurchaseDate = purchaseDate,
                    LastMaintenanceDate = lastMaintenanceDate,
                    NextMaintenanceDate = nextMaintenanceDate,
                    Location = location,
                    Status = status ?? "Available",
                    PurchasePrice = purchasePrice,
                    Category = category
                };

                return await _equipmentService.CreateEquipmentAsync(equipment);
            }
            catch (GraphQLException)
            {
                throw;
            }
            catch (Exception ex)
            {
                throw new GraphQLException(new Error("Internal Server Error", ex.Message));
            }
        }

        public async Task<Equipment?> UpdateEquipment(
            int id,
            string? name = null,
            string? description = null,
            string? serialNumber = null,
            string? model = null,
            string? manufacturer = null,
            DateTime? purchaseDate = null,
            DateTime? lastMaintenanceDate = null,
            DateTime? nextMaintenanceDate = null,
            string? location = null,
            string? status = null,
            decimal? purchasePrice = null,
            string? category = null)
        {
            var existingEquipment = await _equipmentService.GetEquipmentByIdAsync(id);
            if (existingEquipment == null)
                return null;

            var updatedEquipment = new Equipment
            {
                Id = existingEquipment.Id,
                Name = name ?? existingEquipment.Name,
                Description = description ?? existingEquipment.Description,
                SerialNumber = serialNumber ?? existingEquipment.SerialNumber,
                Model = model ?? existingEquipment.Model,
                Manufacturer = manufacturer ?? existingEquipment.Manufacturer,
                PurchaseDate = purchaseDate ?? existingEquipment.PurchaseDate,
                LastMaintenanceDate = lastMaintenanceDate ?? existingEquipment.LastMaintenanceDate,
                NextMaintenanceDate = nextMaintenanceDate ?? existingEquipment.NextMaintenanceDate,
                Location = location ?? existingEquipment.Location,
                Status = status ?? existingEquipment.Status,
                PurchasePrice = purchasePrice ?? existingEquipment.PurchasePrice,
                Category = category ?? existingEquipment.Category
            };

            return await _equipmentService.UpdateEquipmentAsync(id, updatedEquipment);
        }

        public async Task<bool> DeleteEquipment(int id)
        {
            return await _equipmentService.DeleteEquipmentAsync(id);
        }

        // // File Upload Mutations
        public async Task<GraphQLApi.GraphQL.Types.FileUploadResponse> UploadFile(
            GraphQLApi.GraphQL.Types.FileUploadInput input)
        {
            try
            {
                // Validate and convert bucket name string to enum
                if (!Enum.TryParse<Shared.Enums.BucketName>(input.BucketName, true, out var bucketName))
                {
                    return new GraphQLApi.GraphQL.Types.FileUploadResponse
                    {
                        Success = false,
                        ErrorMessage = $"Invalid bucket name: {input.BucketName}. Valid values are: {string.Join(", ", Enum.GetNames<Shared.Enums.BucketName>())}"
                    };
                }

                using var stream = input.File.OpenReadStream();

                var fileMetadata = await _minioService.UploadFileAsync(
                    stream,
                    input.File.Name,
                    bucketName.ToString().ToLowerInvariant(),
                    input.File.ContentType ?? "unkown/unkown",
                    input.Description,
                    input.FolderPath,
                    input.IsPublic,
                    input.ExpiresAt);

                // Update additional metadata if provided
                if (!string.IsNullOrEmpty(input.AdditionalMetadata) && fileMetadata != null)
                {
                    using var context = await _contextFactory.CreateDbContextAsync();
                    var dbFileMetadata = await context.FileMetadata.FindAsync(fileMetadata.Id);
                    if (dbFileMetadata != null)
                    {
                        dbFileMetadata.AdditionalMetadata = input.AdditionalMetadata;
                        await context.SaveChangesAsync();
                        fileMetadata.AdditionalMetadata = input.AdditionalMetadata;
                    }
                }

                // Generate presigned URL if requested or if file is public
                string? presignedUrl = null;
                if (input.IsPublic && fileMetadata != null)
                {
                    presignedUrl = await _minioService.GetPresignedUrlAsync(fileMetadata, 3600); // 1 hour expiration
                }

                return new GraphQLApi.GraphQL.Types.FileUploadResponse
                {
                    Success = fileMetadata != null,
                    FileMetadata = fileMetadata,
                    PresignedUrl = presignedUrl,
                    ErrorMessage = fileMetadata == null ? "Failed to upload file" : null
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error uploading file {FileName}", input.File.Name);
                return new GraphQLApi.GraphQL.Types.FileUploadResponse
                {
                    Success = false,
                    ErrorMessage = "An error occurred while uploading the file"
                };
            }
        }

        public async Task<GraphQLApi.GraphQL.Types.PresignedUrlResponse> GetPresignedUrl(
            GraphQLApi.GraphQL.Types.PresignedUrlInput input)
        {
            try
            {
                using var context = await _contextFactory.CreateDbContextAsync();
                var fileMetadata = await context.FileMetadata
                    .FirstOrDefaultAsync(f => f.Id == input.FileId && !f.IsDeleted);

                if (fileMetadata == null)
                {
                    return new GraphQLApi.GraphQL.Types.PresignedUrlResponse
                    {
                        Success = false,
                        ErrorMessage = "File not found"
                    };
                }

                var presignedUrl = await _minioService.GetPresignedUrlAsync(
                    fileMetadata,
                    input.ExpirationMinutes * 60); // Convert minutes to seconds

                return new GraphQLApi.GraphQL.Types.PresignedUrlResponse
                {
                    Success = !string.IsNullOrEmpty(presignedUrl),
                    Url = presignedUrl,
                    ExpiresAt = DateTime.UtcNow.AddMinutes(input.ExpirationMinutes),
                    ErrorMessage = string.IsNullOrEmpty(presignedUrl) ? "Failed to generate presigned URL" : null
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error generating presigned URL for file {FileId}", input.FileId);
                return new GraphQLApi.GraphQL.Types.PresignedUrlResponse
                {
                    Success = false,
                    ErrorMessage = "An error occurred while generating the presigned URL"
                };
            }
        }

        public async Task<bool> DeleteFile(int fileId)
        {
            try
            {
                using var context = await _contextFactory.CreateDbContextAsync();
                var fileMetadata = await context.FileMetadata
                    .FirstOrDefaultAsync(f => f.Id == fileId && !f.IsDeleted);

                if (fileMetadata == null)
                {
                    return false;
                }

                var success = await _minioService.DeleteFileAsync(
                    fileMetadata.BucketName,
                    fileMetadata.ObjectKey);

                if (success)
                {
                    // Soft delete the metadata record
                    fileMetadata.IsDeleted = true;
                    fileMetadata.DeletedAt = DateTime.UtcNow;
                    fileMetadata.DeletedBy = "System"; // TODO: Get current user

                    await context.SaveChangesAsync();
                    return true;
                }

                return false;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error deleting file {FileId}", fileId);
                return false;
            }
        }
    }
public class UpdateWorkerInput
{
    public int Id { get; set; }
    public string? Name { get; set; }
    public string? Company { get; set; }
    public DateOnly? DateOfBirth { get; set; }
    public List<int>? TrainingIds { get; set; }
    public List<int>? TradeIds { get; set; }
    public List<int>? SkillIds { get; set; }
    public int? ManHours { get; set; }
    public double? Rating { get; set; }
    public string? Gender { get; set; }
    public string? PhoneNumber { get; set; }
    public string? MpesaNumber { get; set; }
    public string? Email { get; set; }
    public DateTime? InductionDate { get; set; }
    public DateTime? MedicalCheckDate { get; set; }
    public IFile? ProfilePicture { get; set; }
    public IFile? Signature { get; set; }
    public List<DocumentFileInput>? Documents { get; set; }
}

    public class CreateWorkerInput
    {
        public string Name { get; set; }
        public string Company { get; set; }
        public string NationalId { get; set; }
        public string Gender { get; set; }
        public string PhoneNumber { get; set; }
        public DateOnly? DateOfBirth { get; set; }
        public List<int>? TrainingIds { get; set; }
        public List<int>? TradeIds { get; set; }
        public List<int>? SkillIds { get; set; }
        public string? MpesaNumber { get; set; }
        public string? Email { get; set; }
        public DateTime? InductionDate { get; set; }
        public DateTime? MedicalCheckDate { get; set; }
        public IFile? ProfilePicture { get; set; }
        public IFile? Signature { get; set; }
        public List<DocumentFileInput>? Documents { get; set; }
    }

    // Site Mutations
    public async Task<Site> CreateSite(string name, SiteDataInput? siteData = null, Guid? tenantId = null)
    {
        var siteDataDto = siteData?.ToDto();
        return await _siteService.CreateSiteAsync(name, siteDataDto, tenantId);
    }

    public async Task<Site?> UpdateSite(Guid id, string? name = null, SiteDataInput? siteData = null, Guid? tenantId = null)
    {
        var siteDataDto = siteData?.ToDto();
        return await _siteService.UpdateSiteAsync(id, name, siteDataDto, tenantId);
    }

    public async Task<Site?> UpdateSiteData(Guid id, SiteDataInput siteData, Guid? tenantId = null)
    {
        var siteDataDto = siteData.ToDto();
        return await _siteService.UpdateSiteDataAsync(id, siteDataDto, tenantId);
    }

    public async Task<Site?> PatchSiteData(Guid id, string jsonPatch, Guid? tenantId = null)
    {
        return await _siteService.PatchSiteDataAsync(id, jsonPatch, tenantId);
    }

    public async Task<bool> DeleteSite(Guid id, Guid? tenantId = null)
    {
        return await _siteService.DeleteSiteAsync(id, tenantId);
    }

    public async Task<Site?> CloneSite(Guid sourceId, string newName, Guid? tenantId = null)
    {
        return await _siteService.CloneSiteAsync(sourceId, newName, tenantId);
    }
}
